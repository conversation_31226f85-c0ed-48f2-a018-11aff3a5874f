package utils

import (
	"bytes"
	"fmt"
	"html/template"
	"strings"

	"github.com/solarchapter-tech/water-iq-backend/config.go"
	"github.com/solarchapter-tech/water-iq-backend/model"
	"gopkg.in/gomail.v2"
)

func SendMail(templatePath string, data model.SendMail, subject string) {
	var body bytes.Buffer
	t, err := template.ParseFiles(templatePath)
	if err != nil {
		fmt.Println(err)
		return
	}
	t.Execute(&body, data)

	subject = fmt.Sprintf("[%s] %s", strings.ToUpper(config.GetConfig().Env), subject)

	m := gomail.NewMessage()
	m.SetHeader("From", config.GetConfig().SendFromAddress)
	m.SetHeader("To", data.SendTo)
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", body.String())

	d := gomail.NewDialer("smtp.gmail.com", 587, config.GetConfig().SendFromAddress, config.GetConfig().MailPassword)

	if err := d.DialAndSend(m); err != nil {
		panic(err)
	}
}
