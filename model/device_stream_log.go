package model

import "time"

type DeviceStreamLog struct {
	ID             int64     `gorm:"column:id;primary_key"`
	DeviceID       int64     `gorm:"column:device_id"`
	Value          string    `gorm:"column:value"`
	UnixDeviceTime int64     `gorm:"column:unix_device_time"`
	DeviceTime     time.Time `gorm:"column:device_time"`
	CreatedAt      time.Time `gorm:"column:created_at"`

	Device Device `gorm:"foreignkey:DeviceID"`
}

type LogStreamRequest struct {
	DeviceCode string `json:"device_code"`
	Log        string `json:"log"`
	Timestamp  int64  `json:"timestamp"`
	DeviceID   int64  // Internal field set during validation
}
