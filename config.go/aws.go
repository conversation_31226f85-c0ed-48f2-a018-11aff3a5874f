package config

import (
	"fmt"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
)

var s3Client *s3.S3

func AwsInit() {
	config := GetConfig()
	sess, err := session.NewSession(&aws.Config{
		Region:      aws.String(config.AwsRegion),
		Credentials: credentials.NewStaticCredentials(config.AwsAccessKey, config.AwsAccessSecret, ""),
		Endpoint:    aws.String(config.AwsBaseUrl),
	})
	if err != nil {
		fmt.Println("Failed to create AWS session:", err)
		return
	}

	s3Client = s3.New(sess)

	fmt.Println("Connected to AWS S3")
}

func S3Client() *s3.S3 {
	return s3Client
}
