ENV=

DB_USERNAME=
DB_PASSWORD=
DB_NAME=
DB_PORT=
DB_HOST=

JWT_SECRET=

SEND_FROM_ADDRESS=
MAIL_PASSWORD=

FRONTEND_BASE_URL=

SMS_ON=
SMS_BASE_URL=
SMS_USER=
SMS_PASSWORD=

WA_ON=
WA_BASE_URL=
WA_API_KEY=

GCS_BUCKET_URL=

REDIS_ADDRESS=

TESTING_DEVICE_IDS=
TESTING_VILLAGE_IDS=
BLACKLIST_REPORT_VILLAGE_IDS=

VALID_SUM_WP_START_DATE=

ALERT_CRON=
SUM_WP_CRON=

AWS_BASE_URL=
AWS_REGION=
AWS_S3_BUCKET=
AWS_ACCESS_KEY=
AWS_ACCESS_SECRET=