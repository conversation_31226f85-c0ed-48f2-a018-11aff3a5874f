package repository

import (
	"github.com/solarchapter-tech/water-iq-backend/config.go"
	"github.com/solarchapter-tech/water-iq-backend/model"
)

type DeviceVersionRepository interface {
	Insert(deviceVersion *model.DeviceVersion) (*model.DeviceVersion, error)
}

type DeviceVersionRepositoryCtx struct{}

func (c *DeviceVersionRepositoryCtx) Insert(deviceVersion *model.DeviceVersion) (*model.DeviceVersion, error) {
	db := config.DbManager()
	err := db.Create(deviceVersion).Error
	if err != nil {
		return nil, err
	}

	return deviceVersion, nil
}
