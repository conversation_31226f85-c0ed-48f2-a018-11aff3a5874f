package repository

import (
	"github.com/solarchapter-tech/water-iq-backend/config.go"
	"github.com/solarchapter-tech/water-iq-backend/model"
)

type DeviceStreamLogRepository interface {
	Insert(dsls []model.DeviceStreamLog) ([]model.DeviceStreamLog, error)
}

type DeviceStreamLogRepositoryCtx struct{}

func (c *DeviceStreamLogRepositoryCtx) Insert(dsls []model.DeviceStreamLog) ([]model.DeviceStreamLog, error) {
	db := config.DbManager()
	query := "INSERT INTO device_stream_log (device_id, value, unix_device_time, device_time, created_at) VALUES "
	values := []interface{}{}

	for _, dsl := range dsls {
		query += "(?, ?, ?, ?, ?),"
		values = append(values, dsl.DeviceID, dsl.Value, dsl.UnixDeviceTime, dsl.DeviceTime, dsl.CreatedAt)
	}

	query = query[:len(query)-1]

	err := db.Exec(query, values...).Error
	if err != nil {
		return nil, err
	}

	return dsls, nil
}
