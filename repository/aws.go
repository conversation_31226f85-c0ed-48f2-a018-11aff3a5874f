package repository

import (
	"fmt"
	"mime/multipart"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/solarchapter-tech/water-iq-backend/config.go"
	"github.com/solarchapter-tech/water-iq-backend/utils"
)

type AwsRepository interface {
	UploadFile(file *multipart.FileHeader, fileName string) (string, error)
}

type AwsRepositoryCtx struct{}

func (c *AwsRepositoryCtx) UploadFile(file *multipart.FileHeader, fileName string) (string, error) {
	s3Client := config.S3Client()
	cfg := config.GetConfig()
	s3Bucket := cfg.AwsS3Bucket

	reader, err := utils.ReadMultipartFile(file)
	if err != nil {
		return "", err
	}

	contentType := file.Header.Get(utils.HeaderKeyContentType)

	_, err = s3Client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(s3Bucket),
		Key:         aws.String(fileName),
		Body:        reader,
		ContentType: aws.String(contentType),
		ACL:         aws.String("public-read"),
	})
	if err != nil {
		return "", err
	}

	fileURL := generateS3FileURL(cfg.AwsBaseUrl, s3Bucket, fileName)
	return fileURL, nil
}

func generateS3FileURL(baseUrl, bucket, fileName string) string {
	return fmt.Sprintf("%s/%s/%s", baseUrl, bucket, fileName)
}
